{"info": {"_postman_id": "ecom-golang-api-collection", "name": "Ecommerce Golang Clean Architecture API", "description": "Complete API collection for Ecommerce platform built with Golang Clean Architecture\n\nThis collection includes:\n- Authentication & Authorization\n- User Management\n- Product Management\n- Category Management\n- Cart & Checkout\n- Order Management\n- Payment Processing\n- File Upload\n- Reviews & Ratings\n- Wishlist\n- Notifications\n- Analytics\n- Admin Operations\n- Search & Filtering\n- Recommendations\n- Shipping & Inventory\n\nBase URL: {{base_url}}/api/v1\nEnvironment Variables Required:\n- base_url: API base URL\n- jwt_token: Authentication token\n- admin_token: Admin authentication token\n- user_id: Current user ID\n- session_id: Session ID for guest operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "description": "User authentication and authorization endpoints", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user account. All fields except phone are required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has user data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('email');", "    pm.expect(responseJson.data).to.have.property('first_name');", "    pm.expect(responseJson.data).to.have.property('last_name');", "    pm.expect(responseJson.data).to.have.property('role');", "});", "", "pm.test('User role is customer by default', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.role).to.eql('customer');", "});"], "type": "text/javascript"}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"remember_me\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Authenticate user and receive JWT tokens. Returns access token and refresh token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains tokens', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('token');", "    pm.expect(responseJson.data).to.have.property('refresh_token');", "    pm.expect(responseJson.data).to.have.property('user');", "});", "", "pm.test('Store tokens in environment', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set('jwt_token', responseJson.data.token);", "        pm.environment.set('refresh_token', responseJson.data.refresh_token);", "        pm.environment.set('user_id', responseJson.data.user.id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}, "description": "Refresh access token using refresh token. Returns new access token and refresh token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains new tokens', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('token');", "    pm.expect(responseJson.data).to.have.property('refresh_token');", "});", "", "pm.test('Update tokens in environment', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set('jwt_token', responseJson.data.token);", "        pm.environment.set('refresh_token', responseJson.data.refresh_token);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout user and invalidate current JWT token. Requires valid JWT token in Authorization header."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Clear tokens from environment', function () {", "    pm.environment.unset('jwt_token');", "    pm.environment.unset('refresh_token');", "    pm.environment.unset('user_id');", "});"], "type": "text/javascript"}}]}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/forgot-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "forgot-password"]}, "description": "Send password reset email to user. Email must be registered in the system."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_from_email\",\n  \"password\": \"NewSecurePassword123!\",\n  \"password_confirmation\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/reset-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "reset-password"]}, "description": "Reset user password using token from forgot password email. Token expires after certain time."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Password reset successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('reset');", "});"], "type": "text/javascript"}}]}, {"name": "Verify Email (GET)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/verify-email?token=verification_token_from_email", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-email"], "query": [{"key": "token", "value": "verification_token_from_email", "description": "Email verification token from registration email"}, {"name": "Get Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/category/{{category_id}}?page=1&limit=12", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "category", "{{category_id}}"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "12", "description": "Items per page"}]}, "description": "Get products belonging to a specific category with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains products from category', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Trending Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/trending?page=1&limit=8", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "trending"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "8", "description": "Items per page"}]}, "description": "Get trending products based on recent sales and views."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains trending products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Related Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}/related?page=1&limit=6", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}", "related"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "6", "description": "Items per page"}]}, "description": "Get products related to a specific product based on category, tags, and attributes."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains related products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/filters?category_id={{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filters"], "query": [{"key": "category_id", "value": "{{category_id}}", "description": "Filter by category", "disabled": true}]}, "description": "Get available filter options for products (brands, price ranges, attributes, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains filter options', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('brands');", "    pm.expect(responseJson.data).to.have.property('price_ranges');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/tree", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "tree"]}, "description": "Get complete category hierarchy as a tree structure."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category tree', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Root Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/root", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "root"]}, "description": "Get all root level categories (categories without parent)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains root categories', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Children", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}/children", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}", "children"]}, "description": "Get direct children of a specific category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains child categories', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/slug/{{category_slug}}", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "slug", "{{category_slug}}"]}, "description": "Get category details by its slug (SEO-friendly URL)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('slug');", "});"], "type": "text/javascript"}}]}, {"name": "Update Brand", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Nike Updated\",\n  \"slug\": \"nike-updated\",\n  \"description\": \"Updated Nike brand description\",\n  \"logo\": \"https://example.com/nike-logo-new.png\",\n  \"website\": \"https://www.nike.com\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/brands/{{created_brand_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "brands", "{{created_brand_id}}"]}, "description": "Update an existing brand. All fields are required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Brand updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/brands/{{created_brand_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "brands", "{{created_brand_id}}"]}, "description": "Delete a brand permanently. This will also update all products using this brand."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Brand deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, "description": "Verify user email using token from registration email. Token is sent via query parameter."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email verification successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('verified');", "});"], "type": "text/javascript"}}]}, {"name": "Verify Email (POST)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification_token_from_email\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-email", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-email"]}, "description": "Verify user email using token from registration email. Token is sent in request body."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email verification successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('verified');", "});"], "type": "text/javascript"}}]}, {"name": "Resend Verification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-verification", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-verification"]}, "description": "Resend email verification link to user's email address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Verification email sent', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('sent');", "});"], "type": "text/javascript"}}]}, {"name": "Get Verification Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/verification/status", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "verification", "status"]}, "description": "Get current user's verification status including email verification, phone verification, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains verification status', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('email_verified');", "});"], "type": "text/javascript"}}]}]}, {"name": "👤 User Management", "description": "User profile, preferences, and account management", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Get current user's profile information. Requires valid JWT token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user profile', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('email');", "    pm.expect(responseJson.data).to.have.property('first_name');", "    pm.expect(responseJson.data).to.have.property('last_name');", "    pm.expect(responseJson.data).to.have.property('role');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+1234567891\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update current user's profile information. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Profile updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"SecurePassword123!\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "change-password"]}, "description": "Change user's password. Requires current password for verification."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Password changed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('changed');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Get current user's preferences including theme, language, notifications, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains preferences', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"dark\",\n  \"language\": \"en\",\n  \"currency\": \"USD\",\n  \"notifications_enabled\": true,\n  \"email_notifications\": true,\n  \"sms_notifications\": false,\n  \"push_notifications\": true,\n  \"marketing_emails\": false,\n  \"order_updates\": true,\n  \"price_alerts\": true,\n  \"newsletter\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Update user preferences. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Preferences updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Update Theme Preference", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"dark\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences/theme", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences", "theme"]}, "description": "Update user's theme preference (light, dark, auto)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Theme updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Update Language Preference", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences/language", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences", "language"]}, "description": "Update user's language preference (en, vi, fr, es, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Language updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/sessions?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "sessions"], "query": [{"key": "limit", "value": "10", "description": "Number of sessions to return"}, {"key": "offset", "value": "0", "description": "Number of sessions to skip"}]}, "description": "Get current user's active sessions with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains sessions', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Store first session ID for invalidation test", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('first_session_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Invalidate Specific Session", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/sessions/{{first_session_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "sessions", "{{first_session_id}}"]}, "description": "Invalidate a specific user session by session ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Session invalidated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('invalidated');", "});"], "type": "text/javascript"}}]}, {"name": "Invalidate All Sessions", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/sessions", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "sessions"]}, "description": "Invalidate all user sessions except the current one."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('All sessions invalidated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('invalidated');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 OAuth Authentication", "description": "OAuth authentication endpoints for Google and Facebook", "item": [{"name": "Get Google OAuth URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/url", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "url"]}, "description": "Get Google OAuth authorization URL for frontend integration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Google OAuth URL retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('state');", "    pm.expect(responseJson.data.url).to.include('accounts.google.com');", "    ", "    // Store OAuth state for callback", "    pm.environment.set('oauth_state', responseJson.data.state);", "});"], "type": "text/javascript"}}]}, {"name": "Get Facebook OAuth URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/url", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "url"]}, "description": "Get Facebook OAuth authorization URL for frontend integration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Facebook OAuth URL retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('state');", "    pm.expect(responseJson.data.url).to.include('facebook.com');", "    ", "    // Store OAuth state for callback", "    pm.environment.set('facebook_oauth_state', responseJson.data.state);", "});"], "type": "text/javascript"}}]}, {"name": "Google OAuth <PERSON>gin (Redirect)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "login"]}, "description": "Initiate Google OAuth login flow (redirects to Google). This endpoint will redirect to Google OAuth consent screen."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Redirect location contains Google OAuth URL', function () {", "    const location = pm.response.headers.get('Location');", "    pm.expect(location).to.include('accounts.google.com');", "});"], "type": "text/javascript"}}]}, {"name": "Facebook OAuth Login (Redirect)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "login"]}, "description": "Initiate Facebook OAuth login flow (redirects to Facebook). This endpoint will redirect to Facebook OAuth consent screen."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Redirect location contains Facebook OAuth URL', function () {", "    const location = pm.response.headers.get('Location');", "    pm.expect(location).to.include('facebook.com');", "});"], "type": "text/javascript"}}]}, {"name": "Google OAuth <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/callback?code=test_auth_code&state={{oauth_state}}", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "callback"], "query": [{"key": "code", "value": "test_auth_code", "description": "Authorization code from Google OAuth"}, {"key": "state", "value": "{{oauth_state}}", "description": "OAuth state parameter for security"}]}, "description": "Handle Google OAuth callback with authorization code. This endpoint processes the OAuth response from Google and creates/logs in the user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["// Note: This test may redirect to frontend in real implementation", "pm.test('Status code is 302 (redirect to frontend) or 200', function () {", "    pm.expect([200, 302]).to.include(pm.response.code);", "});", "", "if (pm.response.code === 200) {", "    pm.test('OAuth login successful', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "        pm.expect(responseJson.data).to.have.property('token');", "        pm.expect(responseJson.data).to.have.property('user');", "        ", "        // Store tokens", "        pm.environment.set('jwt_token', responseJson.data.token);", "        if (responseJson.data.refresh_token) {", "            pm.environment.set('refresh_token', responseJson.data.refresh_token);", "        }", "        pm.environment.set('user_id', responseJson.data.user.id);", "    });", "}"], "type": "text/javascript"}}]}, {"name": "Facebook OAuth <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/callback?code=test_auth_code&state={{facebook_oauth_state}}", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "callback"], "query": [{"key": "code", "value": "test_auth_code", "description": "Authorization code from Facebook OAuth"}, {"key": "state", "value": "{{facebook_oauth_state}}", "description": "OAuth state parameter for security"}]}, "description": "Handle Facebook OAuth callback with authorization code. This endpoint processes the OAuth response from Facebook and creates/logs in the user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["// Note: This test may redirect to frontend in real implementation", "pm.test('Status code is 302 (redirect to frontend) or 200', function () {", "    pm.expect([200, 302]).to.include(pm.response.code);", "});", "", "if (pm.response.code === 200) {", "    pm.test('OAuth login successful', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "        pm.expect(responseJson.data).to.have.property('token');", "        pm.expect(responseJson.data).to.have.property('user');", "        ", "        // Store tokens", "        pm.environment.set('jwt_token', responseJson.data.token);", "        if (responseJson.data.refresh_token) {", "            pm.environment.set('refresh_token', responseJson.data.refresh_token);", "        }", "        pm.environment.set('user_id', responseJson.data.user.id);", "    });", "}"], "type": "text/javascript"}}]}]}, {"name": "🛍️ Products", "description": "Product catalog, search, filtering, and management", "item": [{"name": "📋 Public Product Operations", "description": "Public product endpoints accessible without authentication", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products?page=1&limit=12&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "products"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "12", "description": "Items per page (default: 12, max: 100)"}, {"key": "sort_by", "value": "created_at", "description": "Sort field: name, price, created_at, updated_at"}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc"}]}, "description": "Get paginated list of all active products with basic filtering and sorting."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains products array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "    pm.expect(responseJson.pagination).to.have.property('page');", "    pm.expect(responseJson.pagination).to.have.property('limit');", "    pm.expect(responseJson.pagination).to.have.property('total');", "});", "", "pm.test('Store first product ID', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('first_product_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{first_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{first_product_id}}"]}, "description": "Get detailed information about a specific product by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains product details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('price');", "    pm.expect(responseJson.data).to.have.property('description');", "    pm.expect(responseJson.data).to.have.property('sku');", "});"], "type": "text/javascript"}}]}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/search?q=laptop&page=1&limit=12&min_price=100&max_price=2000&category_id=&brand_id=&rating=4&sort_by=price&sort_order=asc", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search"], "query": [{"key": "q", "value": "laptop", "description": "Search query (product name, description, tags)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "12", "description": "Items per page"}, {"key": "min_price", "value": "100", "description": "Minimum price filter"}, {"key": "max_price", "value": "2000", "description": "Maximum price filter"}, {"key": "category_id", "value": "", "description": "Filter by category ID"}, {"key": "brand_id", "value": "", "description": "Filter by brand ID"}, {"key": "rating", "value": "4", "description": "Minimum rating filter"}, {"key": "sort_by", "value": "price", "description": "Sort by: name, price, rating, created_at"}, {"key": "sort_order", "value": "asc", "description": "Sort order: asc, desc"}]}, "description": "Search products with advanced filtering options including price range, category, brand, rating, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Search results match query', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        const searchQuery = pm.request.url.query.get('q');", "        if (searchQuery) {", "            const firstProduct = responseJson.data[0];", "            const productText = (firstProduct.name + ' ' + firstProduct.description).toLowerCase();", "            pm.expect(productText).to.include(searchQuery.toLowerCase());", "        }", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Featured Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/featured?page=1&limit=8", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "featured"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "8", "description": "Items per page"}]}, "description": "Get featured products that are marked as featured by admin."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains featured products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('All products are featured', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        responseJson.data.forEach(product => {", "            pm.expect(product.featured).to.be.true;", "        });", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Product Management", "description": "Admin-only product management endpoints (requires admin authentication)", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Laptop\",\n  \"description\": \"High-performance laptop with latest specifications\",\n  \"short_description\": \"Premium laptop for professionals\",\n  \"sku\": \"LAPTOP-001\",\n  \"slug\": \"premium-laptop\",\n  \"meta_title\": \"Premium Laptop - High Performance\",\n  \"meta_description\": \"Buy premium laptop with latest specs\",\n  \"keywords\": \"laptop, premium, high-performance\",\n  \"featured\": true,\n  \"visibility\": \"visible\",\n  \"price\": 1299.99,\n  \"compare_price\": 1499.99,\n  \"cost_price\": 999.99,\n  \"sale_price\": 1199.99,\n  \"sale_start_date\": \"2024-01-01T00:00:00Z\",\n  \"sale_end_date\": \"2024-12-31T23:59:59Z\",\n  \"stock\": 50,\n  \"low_stock_threshold\": 5,\n  \"track_quantity\": true,\n  \"allow_backorder\": false,\n  \"weight\": 2.5,\n  \"dimensions\": {\n    \"length\": 35.0,\n    \"width\": 25.0,\n    \"height\": 2.0\n  },\n  \"requires_shipping\": true,\n  \"shipping_class\": \"standard\",\n  \"tax_class\": \"standard\",\n  \"country_of_origin\": \"US\",\n  \"category_id\": \"{{category_id}}\",\n  \"brand_id\": \"{{brand_id}}\",\n  \"images\": [\n    {\n      \"url\": \"https://example.com/laptop1.jpg\",\n      \"alt_text\": \"Premium Laptop Front View\",\n      \"position\": 0\n    }\n  ],\n  \"tags\": [\"laptop\", \"premium\", \"electronics\"],\n  \"attributes\": [\n    {\n      \"attribute_id\": \"{{color_attribute_id}}\",\n      \"value\": \"Silver\",\n      \"position\": 0\n    }\n  ],\n  \"status\": \"active\",\n  \"product_type\": \"simple\",\n  \"is_digital\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products"]}, "description": "Create a new product. Requires admin authentication. All product fields can be specified."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('sku');", "    ", "    // Store created product ID for other tests", "    pm.environment.set('created_product_id', responseJson.data.id);", "});", "", "pm.test('Product has correct data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "    pm.expect(responseJson.data.sku).to.equal(requestBody.sku);", "    pm.expect(responseJson.data.price).to.equal(requestBody.price);", "});"], "type": "text/javascript"}}]}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Premium Laptop\",\n  \"description\": \"Updated high-performance laptop with latest specifications\",\n  \"price\": 1399.99,\n  \"compare_price\": 1599.99,\n  \"stock\": 45,\n  \"featured\": true,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}"]}, "description": "Update an existing product. All fields are optional. Only provided fields will be updated."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});", "", "pm.test('Product has updated data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "    pm.expect(responseJson.data.price).to.equal(requestBody.price);", "});"], "type": "text/javascript"}}]}, {"name": "Patch Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 1299.99,\n  \"stock\": 30,\n  \"featured\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}"]}, "description": "Partially update a product. Only provided fields will be updated, others remain unchanged."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product patched successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});"], "type": "text/javascript"}}]}, {"name": "Update Product Stock", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"stock\": 25\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}/stock", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}", "stock"]}, "description": "Update product stock quantity. This will also automatically update stock status."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Stock updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}"]}, "description": "Delete a product permanently. This will also remove the product from all carts."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "📂 Categories", "description": "Product category management and hierarchy", "item": [{"name": "📋 Public Category Operations", "description": "Public category endpoints accessible without authentication", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20, max: 100)"}]}, "description": "Get paginated list of all active categories."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains categories array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "});", "", "pm.test('Store first category ID', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('category_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}"]}, "description": "Get detailed information about a specific category by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('slug');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/tree", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "tree"]}, "description": "Get hierarchical category tree structure with parent-child relationships."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category tree', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Category Management", "description": "Admin-only category management endpoints (requires admin authentication)", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electronics\",\n  \"description\": \"Electronic devices and gadgets\",\n  \"slug\": \"electronics\",\n  \"image\": \"https://example.com/electronics.jpg\",\n  \"parent_id\": null,\n  \"is_active\": true,\n  \"sort_order\": 1,\n  \"seo\": {\n    \"meta_title\": \"Electronics - Latest Gadgets and Devices\",\n    \"meta_description\": \"Shop the latest electronics, gadgets, and devices at great prices\",\n    \"keywords\": \"electronics, gadgets, devices, technology\",\n    \"canonical_url\": \"https://example.com/categories/electronics\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories"]}, "description": "Create a new category. Requires admin authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Category created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('slug');", "    ", "    // Store created category ID for other tests", "    pm.environment.set('created_category_id', responseJson.data.id);", "});", "", "pm.test('Category has correct data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "    pm.expect(responseJson.data.slug).to.equal(requestBody.slug);", "});"], "type": "text/javascript"}}]}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Electronics\",\n  \"description\": \"Updated electronic devices and gadgets\",\n  \"is_active\": true,\n  \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories", "{{created_category_id}}"]}, "description": "Update an existing category. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Category updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});", "", "pm.test('Category has updated data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "});"], "type": "text/javascript"}}]}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories", "{{created_category_id}}"]}, "description": "Delete a category permanently. This will also update all products using this category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Category deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🏷️ Brands", "description": "Brand management and information", "item": [{"name": "📋 Public Brand Operations", "description": "Public brand endpoints accessible without authentication", "item": [{"name": "Get All Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands?page=1&limit=20&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "brands"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20, max: 100)"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}]}, "description": "Get paginated list of all brands with optional filtering."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains brands array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Brand by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/{{brand_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "{{brand_id}}"]}, "description": "Get detailed information about a specific brand by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains brand data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "});"], "type": "text/javascript"}}]}, {"name": "Get Brand by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/slug/{{brand_slug}}", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "slug", "{{brand_slug}}"]}, "description": "Get brand details by its slug (SEO-friendly URL)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains brand data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('slug');", "});"], "type": "text/javascript"}}]}, {"name": "Get Active Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/active", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "active"]}, "description": "Get all active brands without pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains active brands', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Popular Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/popular?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "popular"], "query": [{"key": "limit", "value": "10", "description": "Number of popular brands to return"}]}, "description": "Get popular brands based on product count and sales."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains popular brands', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Search Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/search?q=nike&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "search"], "query": [{"key": "q", "value": "nike", "description": "Search query (brand name)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}]}, "description": "Search brands by name with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Brand Management", "description": "Admin-only brand management endpoints (requires admin authentication)", "item": [{"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"slug\": \"nike\",\n  \"description\": \"Just Do It - Leading sports brand\",\n  \"logo\": \"https://example.com/nike-logo.png\",\n  \"website\": \"https://www.nike.com\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/brands", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "brands"]}, "description": "Create a new brand. Requires admin authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Brand created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.environment.set('created_brand_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🛒 Shopping Cart", "description": "Shopping cart operations for authenticated and guest users", "item": [{"name": "📋 Cart Operations", "description": "Shopping cart management endpoints", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "X-Session-ID", "value": "{{session_id}}", "description": "Required for guest cart operations"}], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}, "description": "Get current user's cart or guest cart by session ID. For authenticated users, Authorization header is required. For guest users, X-Session-ID header is required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains cart data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items).to.be.an('array');", "    pm.expect(responseJson.data).to.have.property('subtotal');", "    pm.expect(responseJson.data).to.have.property('total');", "});", "", "pm.test('Store cart ID', function () {", "    const responseJson = pm.response.json();", "    pm.environment.set('cart_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-Session-ID", "value": "{{session_id}}", "description": "Required for guest cart operations"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{first_product_id}}\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items"]}, "description": "Add a product to the shopping cart. Works for both authenticated users and guest users."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Item added to cart successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items.length).to.be.greaterThan(0);", "});", "", "pm.test('Cart totals updated', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.subtotal).to.be.greaterThan(0);", "    pm.expect(responseJson.data.total).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}]}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items/{{first_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{first_product_id}}"]}, "description": "Update the quantity of an item in the cart. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Cart item updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "});", "", "pm.test('Item quantity updated', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    const updatedItem = responseJson.data.items.find(item => item.product.id === pm.environment.get('first_product_id'));", "    if (updatedItem) {", "        pm.expect(updatedItem.quantity).to.equal(requestBody.quantity);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Remove from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart/items/{{first_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{first_product_id}}"]}, "description": "Remove a specific product from the cart. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Item removed from cart successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "});", "", "pm.test('Item no longer in cart', function () {", "    const responseJson = pm.response.json();", "    const removedItem = responseJson.data.items.find(item => item.product.id === pm.environment.get('first_product_id'));", "    pm.expect(removedItem).to.be.undefined;", "});"], "type": "text/javascript"}}]}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}, "description": "Remove all items from the cart. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON><PERSON> cleared successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('cleared');", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"strategy\": \"auto\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/merge", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "merge"]}, "description": "Merge guest cart with user cart when user logs in. Strategy options: auto, replace, keep_user, merge."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON><PERSON> merged successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "💳 Checkout & Orders", "description": "Checkout process and order management", "item": [{"name": "📋 Customer Order Operations", "description": "Customer order endpoints (requires authentication)", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shipping_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"USA\",\n    \"phone\": \"+**********\"\n  },\n  \"billing_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"USA\",\n    \"phone\": \"+**********\"\n  },\n  \"payment_method\": \"bank_transfer\",\n  \"notes\": \"Please deliver after 5 PM\",\n  \"tax_rate\": 0.08,\n  \"shipping_cost\": 10.00,\n  \"discount_amount\": 5.00\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"]}, "description": "Create a new order from user's cart. Currently supports bank transfer payment method only."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Order created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_number');", "    pm.expect(responseJson.data).to.have.property('status');", "    ", "    // Store created order ID for other tests", "    pm.environment.set('order_id', responseJson.data.id);", "    pm.environment.set('order_number', responseJson.data.order_number);", "});", "", "pm.test('Order has correct payment method', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.payment_method).to.equal('bank_transfer');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders?page=1&limit=10&status=&payment_status=&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "status", "value": "", "description": "Filter by order status: pending, confirmed, processing, shipped, delivered, cancelled"}, {"key": "payment_status", "value": "", "description": "Filter by payment status: pending, paid, failed, refunded"}, {"key": "sort_by", "value": "created_at", "description": "Sort by field"}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc"}]}, "description": "Get current user's order history with optional filtering and sorting."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains orders array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}"]}, "description": "Get detailed information about a specific order by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains order details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_number');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/cancel", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "cancel"]}, "description": "Cancel an existing order. Only orders with certain statuses can be cancelled."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Order cancelled successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.status).to.equal('cancelled');", "});"], "type": "text/javascript"}}]}, {"name": "Get Order Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/events?public=true", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "events"], "query": [{"key": "public", "value": "true", "description": "Show only public events (default: false)"}]}, "description": "Get order events/timeline showing order status changes and activities."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains events array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "💰 Payments", "description": "Payment processing, refunds, and payment methods", "item": [{"name": "📋 Payment Operations", "description": "Payment processing and management endpoints", "item": [{"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 99.99,\n  \"currency\": \"USD\",\n  \"description\": \"Order payment\",\n  \"success_url\": \"https://example.com/success\",\n  \"cancel_url\": \"https://example.com/cancel\",\n  \"metadata\": {\n    \"customer_id\": \"{{user_id}}\",\n    \"order_number\": \"{{order_number}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments/checkout-session", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "checkout-session"]}, "description": "Create a Stripe checkout session for payment processing."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Checkout session created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('session_id');", "    pm.expect(responseJson.data).to.have.property('session_url');", "    ", "    // Store session info for other tests", "    pm.environment.set('checkout_session_id', responseJson.data.session_id);", "    pm.environment.set('checkout_session_url', responseJson.data.session_url);", "});", "", "pm.test('Session URL is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.session_url).to.include('https://');", "});"], "type": "text/javascript"}}]}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 99.99,\n  \"currency\": \"USD\",\n  \"method\": \"credit_card\",\n  \"payment_token\": \"tok_visa\",\n  \"billing_address\": {\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"address1\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"US\"\n  },\n  \"metadata\": {\n    \"customer_id\": \"{{user_id}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments", "host": ["{{base_url}}"], "path": ["api", "v1", "payments"]}, "description": "Process a direct payment for an order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Payment processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.expect(responseJson.data).to.have.property('transaction_id');", "    ", "    // Store payment ID for other tests", "    pm.environment.set('payment_id', responseJson.data.id);", "});", "", "pm.test('Payment has correct amount', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.amount).to.equal(requestBody.amount);", "});"], "type": "text/javascript"}}]}, {"name": "Get Payment by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/payments/{{payment_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "{{payment_id}}"]}, "description": "Get detailed information about a specific payment by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains payment details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_id');", "    pm.expect(responseJson.data).to.have.property('amount');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.expect(responseJson.data).to.have.property('method');", "});"], "type": "text/javascript"}}]}, {"name": "Process Refund", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_id\": \"{{payment_id}}\",\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 50.00,\n  \"reason\": \"customer_request\",\n  \"description\": \"Customer requested refund for damaged item\",\n  \"type\": \"partial\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments/{{payment_id}}/refund", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "{{payment_id}}", "refund"]}, "description": "Process a refund for a payment. Can be partial or full refund."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Refund processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('status');", "    ", "    // Store refund ID for other tests", "    pm.environment.set('refund_id', responseJson.data.id);", "});", "", "pm.test('Refund has correct amount', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.amount).to.equal(requestBody.amount);", "});"], "type": "text/javascript"}}]}, {"name": "Get Order Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/payments", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "payments"]}, "description": "Get all payments associated with a specific order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains payments array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "⭐ Reviews & Ratings", "description": "Product reviews, ratings, and feedback management", "item": [{"name": "Get Product Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}/reviews?page=1&limit=10&rating=5&verified=true&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}", "reviews"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of reviews per page"}, {"key": "rating", "value": "5", "description": "Filter by rating (1-5)", "disabled": true}, {"key": "verified", "value": "true", "description": "Filter by verified purchase", "disabled": true}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, rating, helpful_count)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)", "disabled": true}]}, "description": "Get reviews for a specific product with filtering and pagination options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains reviews data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Rating Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}/rating", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}", "rating"]}, "description": "Get rating summary for a product including average rating and rating distribution."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains rating data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('average_rating');", "    pm.expect(responseJson.data).to.have.property('total_reviews');", "});"], "type": "text/javascript"}}]}, {"name": "Create Product Review", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"rating\": 5,\n  \"title\": \"Excellent product!\",\n  \"comment\": \"This product exceeded my expectations. Great quality and fast delivery.\",\n  \"pros\": [\"Great quality\", \"Fast delivery\", \"Good value\"],\n  \"cons\": [\"Packaging could be better\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/reviews", "host": ["{{base_url}}"], "path": ["api", "v1", "reviews"]}, "description": "Create a new product review. User must have purchased the product to leave a review."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Review created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('rating');", "    ", "    // Store review ID for future tests", "    pm.environment.set('review_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Get User Reviews", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/reviews/user?page=1&limit=10&rating=5&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "reviews", "user"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of reviews per page"}, {"key": "rating", "value": "5", "description": "Filter by rating (1-5)", "disabled": true}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, rating)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)", "disabled": true}]}, "description": "Get all reviews written by the current user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user reviews', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}]}, {"name": "❤️ Wishlist", "description": "User wishlist management", "item": [{"name": "Get User Wishlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of items per page"}]}, "description": "Get current user's wishlist with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains wishlist data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Add Product to Wishlist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/wishlist", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist"]}, "description": "Add a product to user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product added to wishlist', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('added');", "});"], "type": "text/javascript"}}]}, {"name": "Remove Product from Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist", "{{product_id}}"]}, "description": "Remove a product from user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product removed from wishlist', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('removed');", "});"], "type": "text/javascript"}}]}, {"name": "Check Wishlist Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist/{{product_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist", "{{product_id}}", "status"]}, "description": "Check if a specific product is in user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains wishlist status', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('in_wishlist');", "    pm.expect(responseJson.data.in_wishlist).to.be.a('boolean');", "});"], "type": "text/javascript"}}]}]}, {"name": "📍 Addresses", "description": "User address management", "item": [{"name": "Get User Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "addresses"]}, "description": "Get all addresses for the current user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains addresses', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"home\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"company\": \"Tech Corp\",\n  \"address_line_1\": \"123 Main Street\",\n  \"address_line_2\": \"Apt 4B\",\n  \"city\": \"New York\",\n  \"state\": \"NY\",\n  \"postal_code\": \"10001\",\n  \"country\": \"US\",\n  \"phone\": \"+**********\",\n  \"is_default\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "addresses"]}, "description": "Create a new address for the user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Address created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('address_line_1');", "    ", "    // Store address ID for future tests", "    pm.environment.set('address_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"work\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"company\": \"Updated Tech Corp\",\n  \"address_line_1\": \"456 Updated Street\",\n  \"address_line_2\": \"Suite 10\",\n  \"city\": \"Boston\",\n  \"state\": \"MA\",\n  \"postal_code\": \"02101\",\n  \"country\": \"US\",\n  \"phone\": \"+1987654321\",\n  \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/addresses/{{address_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "addresses", "{{address_id}}"]}, "description": "Update an existing address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Address updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data.company).to.include('Updated');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/addresses/{{address_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "addresses", "{{address_id}}"]}, "description": "Delete an address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Address deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}, {"name": "🎫 Coupons & Promotions", "description": "Discount coupons and promotional campaigns", "item": []}, {"name": "📁 File Management", "description": "File upload, management, and serving", "item": [{"name": "📋 File Upload Operations", "description": "File upload endpoints for different user types", "item": [{"name": "Upload Image (User)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "image"]}, "description": "Upload an image file for authenticated users. Supports JPG, PNG, GIF, WebP formats with max size of 5MB."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson).to.have.property('fileName');", "    pm.expect(responseJson).to.have.property('fileSize');", "    pm.expect(responseJson).to.have.property('message');", "    ", "    // Store file info for other tests", "    pm.environment.set('uploaded_file_id', responseJson.id);", "    pm.environment.set('uploaded_file_url', responseJson.url);", "});", "", "pm.test('File URL is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.url).to.include('/uploads/');", "});"], "type": "text/javascript"}}]}, {"name": "Upload Image (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/admin/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "upload", "image"]}, "description": "Upload an image file for admin users. Requires admin authentication. Used for product images, category images, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Admin file uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson.url).to.include('/uploads/admin/');", "    ", "    // Store admin file info", "    pm.environment.set('admin_file_id', responseJson.id);", "});"], "type": "text/javascript"}}]}, {"name": "Upload Image (Public)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/public/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "public", "upload", "image"]}, "description": "Upload an image file without authentication. Used for guest reviews, comments, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Public file uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson.url).to.include('/uploads/public/');", "    ", "    // Store public file info", "    pm.environment.set('public_file_id', responseJson.id);", "});"], "type": "text/javascript"}}]}, {"name": "Upload Document (User)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Document file to upload (PDF, DOC, DOCX, TXT, max 10MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/upload/document", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "document"]}, "description": "Upload a document file for authenticated users. Supports PDF, DOC, DOCX, TXT formats with max size of 10MB."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Document uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson.contentType).to.match(/application|text/);", "    ", "    // Store document info", "    pm.environment.set('uploaded_document_id', responseJson.id);", "});"], "type": "text/javascript"}}]}]}, {"name": "📋 File Management Operations", "description": "File retrieval and management endpoints", "item": [{"name": "Get File Upload Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/files/{{uploaded_file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "files", "{{uploaded_file_id}}"]}, "description": "Get detailed information about a specific uploaded file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File info retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('fileName');", "    pm.expect(responseJson.data).to.have.property('originalName');", "    pm.expect(responseJson.data).to.have.property('fileSize');", "    pm.expect(responseJson.data).to.have.property('contentType');", "    pm.expect(responseJson.data).to.have.property('url');", "});"], "type": "text/javascript"}}]}, {"name": "Get All File Uploads", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/files?page=1&limit=20&category=images&upload_type=user", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "files"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of files per page"}, {"key": "category", "value": "images", "description": "Filter by file category (images, documents)"}, {"key": "upload_type", "value": "user", "description": "Filter by upload type (admin, user, public)"}]}, "description": "Get a paginated list of all uploaded files with optional filtering."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Files list retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Delete File", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/files/{{uploaded_file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "files", "{{uploaded_file_id}}"]}, "description": "Delete an uploaded file. This will remove both the file record and the physical file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🔔 Notifications", "description": "User notifications and preferences", "item": []}, {"name": "🔍 Search & Filtering", "description": "Advanced search, filtering, and autocomplete", "item": []}, {"name": "🎯 Recommendations", "description": "Product recommendations and personalization", "item": []}, {"name": "🚚 Shipping & Inventory", "description": "Shipping methods, tracking, and inventory management", "item": []}, {"name": "🔔 Notifications", "description": "User notifications and real-time messaging", "item": [{"name": "Get User Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications?page=1&limit=15&unread_only=false", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "15", "description": "Number of notifications per page"}, {"key": "unread_only", "value": "false", "description": "Show only unread notifications", "disabled": true}]}, "description": "Get user's notifications with pagination and filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains notifications', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Unread Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/unread-count", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "unread-count"]}, "description": "Get count of unread notifications for the user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains unread count', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data.count).to.be.a('number');", "});"], "type": "text/javascript"}}]}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/{{notification_id}}/read", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "{{notification_id}}", "read"]}, "description": "Mark a specific notification as read."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Notification marked as read', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('read');", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON> as <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/mark-all-read", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "mark-all-read"]}, "description": "Mark all notifications as read for the current user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('All notifications marked as read', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('All notifications');", "});"], "type": "text/javascript"}}]}]}, {"name": "📊 Analytics", "description": "Analytics events and metrics tracking", "item": []}, {"name": "🔧 Admin Panel", "description": "Administrative operations and management", "item": [{"name": "📊 Dashboard & Analytics", "description": "Admin dashboard and system analytics", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard?period=30d&include_charts=true", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard"], "query": [{"key": "period", "value": "30d", "description": "Time period for dashboard data (7d, 30d, 90d, 1y)"}, {"key": "include_charts", "value": "true", "description": "Include chart data in response"}]}, "description": "Get comprehensive admin dashboard data including overview statistics, charts, and recent activity."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Dashboard data retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('overview');", "    pm.expect(responseJson.data.overview).to.have.property('totalRevenue');", "    pm.expect(responseJson.data.overview).to.have.property('totalOrders');", "    pm.expect(responseJson.data.overview).to.have.property('totalUsers');", "    pm.expect(responseJson.data.overview).to.have.property('totalProducts');", "});"], "type": "text/javascript"}}]}, {"name": "Get System Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard", "stats"]}, "description": "Get detailed system statistics and performance metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('System stats retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('systemHealth');", "});"], "type": "text/javascript"}}]}, {"name": "Get Sales Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/sales?period=30d&group_by=day", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "sales"], "query": [{"key": "period", "value": "30d", "description": "Time period for analytics (7d, 30d, 90d, 1y)"}, {"key": "group_by", "value": "day", "description": "Group data by (day, week, month)"}]}, "description": "Get detailed sales analytics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Sales analytics retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('totalRevenue');", "    pm.expect(responseJson.data).to.have.property('totalOrders');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/products?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "products"], "query": [{"key": "limit", "value": "10", "description": "Number of top products to return"}]}, "description": "Get product performance analytics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product analytics retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('topProducts');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/users?period=30d", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "users"], "query": [{"key": "period", "value": "30d", "description": "Time period for user analytics"}]}, "description": "Get user behavior analytics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User analytics retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('totalUsers');", "    pm.expect(responseJson.data).to.have.property('newUsers');", "});"], "type": "text/javascript"}}]}]}, {"name": "👥 User Management", "description": "Admin user management operations", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/users?page=1&limit=25&role=customer&status=active&search=john&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "25", "description": "Number of users per page"}, {"key": "role", "value": "customer", "description": "Filter by user role (customer, admin, moderator)"}, {"key": "status", "value": "active", "description": "Filter by user status (active, inactive, suspended)"}, {"key": "search", "value": "john", "description": "Search by name or email"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, name, email, last_login)"}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)"}]}, "description": "Get paginated list of all users with advanced filtering and sorting options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Users list retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('users');", "    pm.expect(responseJson.data.users).to.be.an('array');", "    pm.expect(responseJson.data).to.have.property('pagination');", "    ", "    // Store first user ID for other tests", "    if (responseJson.data.users.length > 0) {", "        pm.environment.set('admin_user_id', responseJson.data.users[0].id);", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "📊 Reports & System", "description": "Report generation and system management", "item": [{"name": "Generate Report", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"sales\",\n  \"format\": \"pdf\",\n  \"date_from\": \"2024-01-01\",\n  \"date_to\": \"2024-12-31\",\n  \"filters\": {\n    \"category_id\": null,\n    \"product_id\": null\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/reports/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports", "generate"]}, "description": "Generate various types of reports (sales, inventory, users, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Report generation initiated', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('report_id');", "    ", "    // Store report ID for future tests", "    pm.environment.set('report_id', responseJson.data.report_id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Reports List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/reports?page=1&limit=20&type=sales&status=completed", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "type", "value": "sales", "description": "Report type filter", "disabled": true}, {"key": "status", "value": "completed", "description": "Report status filter", "disabled": true}]}, "description": "Get list of generated reports with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains reports list', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Download Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/reports/{{report_id}}/download", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports", "{{report_id}}", "download"]}, "description": "Download a generated report file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is a file', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/');", "});"], "type": "text/javascript"}}]}, {"name": "Get System Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/system/logs?level=error&limit=100&from=2024-01-01", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "system", "logs"], "query": [{"key": "level", "value": "error", "description": "Log level filter (debug, info, warn, error)", "disabled": true}, {"key": "limit", "value": "100", "description": "Number of log entries"}, {"key": "from", "value": "2024-01-01", "description": "Start date for logs", "disabled": true}]}, "description": "Get system logs with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains logs', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "👥 Moderator Panel", "description": "Moderator operations and content management", "item": []}, {"name": "🌐 WebSocket", "description": "Real-time notifications and WebSocket connections", "item": []}, {"name": "⚙️ System & Utilities", "description": "Health checks, migrations, and system utilities", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check system health and status."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('System is healthy', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('status');", "    pm.expect(responseJson.status).to.equal('healthy');", "});"], "type": "text/javascript"}}]}, {"name": "API Version Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/version", "host": ["{{base_url}}"], "path": ["api", "v1", "version"]}, "description": "Get API version and build information."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains version info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('version');", "    pm.expect(responseJson).to.have.property('build_time');", "});"], "type": "text/javascript"}}]}, {"name": "Validate Coupon", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"SAVE20\",\n  \"cart_total\": 100.00,\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/coupons/validate", "host": ["{{base_url}}"], "path": ["api", "v1", "coupons", "validate"]}, "description": "Validate a coupon code and get discount information."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Coupon validation response', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('valid');", "    pm.expect(responseJson.data).to.have.property('discount_amount');", "});"], "type": "text/javascript"}}]}, {"name": "Get Shipping Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/methods", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "methods"]}, "description": "Get available shipping methods and rates."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping methods', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Calculate Shipping Cost", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"destination\": {\n    \"country\": \"US\",\n    \"state\": \"CA\",\n    \"city\": \"San Francisco\",\n    \"postal_code\": \"94102\"\n  },\n  \"items\": [\n    {\n      \"product_id\": \"{{product_id}}\",\n      \"quantity\": 2,\n      \"weight\": 1.5,\n      \"dimensions\": {\n        \"length\": 10,\n        \"width\": 8,\n        \"height\": 6\n      }\n    }\n  ],\n  \"shipping_method\": \"standard\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/rates", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "rates"]}, "description": "Calculate shipping cost for items to a specific destination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping cost', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('cost');", "    pm.expect(responseJson.data).to.have.property('estimated_delivery');", "});"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "// Set common headers and handle authentication", "", "// Set Content-Type for JSON requests", "if (pm.request.method !== 'GET' && pm.request.body && pm.request.body.mode === 'raw') {", "    pm.request.headers.add({", "        key: 'Content-Type',", "        value: 'application/json'", "    });", "}", "", "// Add Authorization header if jwt_token exists", "const token = pm.environment.get('jwt_token') || pm.globals.get('jwt_token');", "if (token) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + token", "    });", "}", "", "// Add session ID for guest operations", "const sessionId = pm.environment.get('session_id') || pm.globals.get('session_id');", "if (sessionId && !token) {", "    pm.request.headers.add({", "        key: 'X-Session-ID',", "        value: sessionId", "    });", "}", "", "// Generate session ID if not exists", "if (!sessionId) {", "    const newSessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);", "    pm.environment.set('session_id', newSessionId);", "    pm.request.headers.add({", "        key: 'X-Session-ID',", "        value: newSessionId", "    });", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "// Handle common response patterns and token management", "", "// Log response for debugging", "console.log('Response Status:', pm.response.code);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "// Handle authentication responses", "if (pm.response.code === 200 || pm.response.code === 201) {", "    try {", "        const responseJson = pm.response.json();", "        ", "        // Store JWT token from login/register responses", "        if (responseJson.data && responseJson.data.token) {", "            pm.environment.set('jwt_token', responseJson.data.token);", "            console.log('JWT token stored');", "        }", "        ", "        // Store refresh token", "        if (responseJson.data && responseJson.data.refresh_token) {", "            pm.environment.set('refresh_token', responseJson.data.refresh_token);", "            console.log('Refresh token stored');", "        }", "        ", "        // Store user ID", "        if (responseJson.data && responseJson.data.user && responseJson.data.user.id) {", "            pm.environment.set('user_id', responseJson.data.user.id);", "            console.log('User ID stored');", "        }", "        ", "        // Store created resource IDs", "        if (responseJson.data && responseJson.data.id) {", "            const requestName = pm.info.requestName.toLowerCase();", "            if (requestName.includes('product')) {", "                pm.environment.set('last_product_id', responseJson.data.id);", "            } else if (requestName.includes('category')) {", "                pm.environment.set('last_category_id', responseJson.data.id);", "            } else if (requestName.includes('order')) {", "                pm.environment.set('last_order_id', responseJson.data.id);", "            }", "        }", "    } catch (e) {", "        console.log('Response is not JSON or error parsing:', e.message);", "    }", "}", "", "// Handle 401 Unauthorized - clear tokens", "if (pm.response.code === 401) {", "    pm.environment.unset('jwt_token');", "    pm.environment.unset('refresh_token');", "    console.log('Tokens cleared due to 401 response');", "}", "", "// Basic response validation", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid status code', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 202, 204, 400, 401, 403, 404, 422, 500]);", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "api_version", "value": "v1", "type": "string"}]}